import { FinanceKPIRegionWiseResponce } from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableColumnSeperateCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { ReportRowNoData } from '../../ReportsCommonComponents/ReportRowNoData';
import { downloadExcelYTDFullYear } from '../utils/exportDownloadFormatters';
import {
  formatValue,
  regionSplitRowOrderTable,
} from '../utils/helperFinanceKpi';

interface PropsTypes {
  summaryData: FinanceKPIRegionWiseResponce[];
  filters: ReportFilters;
  datePeriod: string;
}

const columnWidth = 'w-[110px]';

export default function FinancialYTDFullYearTable(props: PropsTypes) {
  const { summaryData, filters, datePeriod } = props;

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF] "
          onClick={() => downloadExcelYTDFullYear(summaryData, filters)}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div>
        <CommonTable>
          <thead>
            <CommonTableMainHeaderRow>
              <th></th>
              <CommonTableHeadingMergeCell colSpan={3}>
                YTD vs. Budget
              </CommonTableHeadingMergeCell>
              <CommonTableColumnSeperateCell />
              <CommonTableHeadingMergeCell colSpan={3}>
                Full Year Expected vs. Budget
              </CommonTableHeadingMergeCell>
            </CommonTableMainHeaderRow>

            <CommonTableSubHeaderRow>
              <CommonTableHeadingCell
                subHeading={false}
                className={`${columnWidth}`}
                // fontWeight="font-bold"
                // textAlign="text-start"
                // fontSize="text-base"
              >
                {''}
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`} borderLeft>
                Actual
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Budget
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`} borderRight>
                Variance
              </CommonTableHeadingCell>
              <CommonTableColumnSeperateCell />
              <CommonTableHeadingCell className={`${columnWidth}`} borderLeft>
                Expected
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Budget
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`} borderRight>
                Variance
              </CommonTableHeadingCell>
            </CommonTableSubHeaderRow>
          </thead>

          <tbody>
            {summaryData?.length === 0 ? (
              <ReportRowNoData colSpan={6} />
            ) : (
              regionSplitRowOrderTable?.map(({ label, key, unit }) => {
                const item = summaryData?.find(
                  (item) => item?.FeesType === key,
                );

                return (
                  <CommonTableBodyRow key={key}>
                    <CommonTableBodyCell textAlign="text-start" borderRight>
                      {/* {item?.FeesType} */}
                      {label}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell>
                      {formatValue(item?.Actual, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell>
                      {formatValue(item?.budget, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell borderRight>
                      {formatValue(item?.Variance, unit)}
                    </CommonTableBodyCell>
                    <CommonTableColumnSeperateCell />
                    <CommonTableBodyCell borderLeft>
                      {formatValue(item?.forecast, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell>
                      {formatValue(item?.budget1, unit)}
                    </CommonTableBodyCell>
                    <CommonTableBodyCell borderRight>
                      {formatValue(item?.Variance1, unit)}
                    </CommonTableBodyCell>
                  </CommonTableBodyRow>
                );
              })
            )}

            {/*  */}
          </tbody>
        </CommonTable>
      </div>
    </>
  );
}
