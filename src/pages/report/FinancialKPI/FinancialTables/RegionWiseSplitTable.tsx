import React from 'react';
import { FinanceKPIRegionWiseResponce } from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableColumnSeperateCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { ReportRowNoData } from '../../ReportsCommonComponents/ReportRowNoData';
import { exportRegionWiseSplitPropertyToExcel } from '../utils/exportDownloadFormatters';
import {
  formatValue,
  regionSplitRowOrderTable,
} from '../utils/helperFinanceKpi';

interface PropsTypes {
  tableData: FinanceKPIRegionWiseResponce[];
  filters: ReportFilters;
  datePeriod: string;
}

const columnsWidth = 'w-[110px]';

const RegionWiseSplitTable = (props: PropsTypes) => {
  const { tableData, filters, datePeriod } = props;

  const groupedData = tableData.reduce(
    (acc, curr) => {
      if (!acc[curr.Region]) acc[curr.Region] = {};
      acc[curr.Region][curr.FeesType] = curr;
      return acc;
    },
    {} as Record<string, Record<string, (typeof tableData)[number]>>,
  );

  return (
    <>
      <div className="mb-10">
        <div className="mb-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-[#43298F]">
            Period : {datePeriod}
          </h3>
          <Button
            variant="outline"
            className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
            onClick={() =>
              exportRegionWiseSplitPropertyToExcel(
                Object.entries(groupedData),
                filters,
              )
            }
          >
            <FileUp className="mr-2 h-4 w-4" />
            Export Excel
          </Button>
        </div>

        <CommonTable>
          <thead>
            <CommonTableMainHeaderRow>
              <th></th>
              <CommonTableHeadingMergeCell colSpan={3}>
                YTD Act. Vs. Budget
              </CommonTableHeadingMergeCell>
              <CommonTableColumnSeperateCell />
              <CommonTableHeadingMergeCell colSpan={3}>
                Full Year Exp. Vs. Budget
              </CommonTableHeadingMergeCell>
            </CommonTableMainHeaderRow>
          </thead>
          <tbody>
            {tableData?.length === 0 && <ReportRowNoData colSpan={6} />}
            {Object.entries(groupedData)?.map(([region, feesMap]) => {
              return (
                <React.Fragment key={region}>
                  <CommonTableBodyRow>
                    <CommonTableHeadingCell
                      subHeading={false}
                      className={`${columnsWidth} leading-0`}
                      fontWeight="font-bold"
                      textAlign="text-start"
                      fontSize="text-base"
                    >
                      {region}
                    </CommonTableHeadingCell>
                    <CommonTableHeadingCell
                      className={`${columnsWidth}`}
                      borderLeft
                    >
                      Actual
                    </CommonTableHeadingCell>
                    <CommonTableHeadingCell className={`${columnsWidth}`}>
                      Budget
                    </CommonTableHeadingCell>
                    <CommonTableHeadingCell
                      className={`${columnsWidth}`}
                      borderRight
                    >
                      Variance
                    </CommonTableHeadingCell>
                    <CommonTableColumnSeperateCell />
                    <CommonTableHeadingCell
                      className={`${columnsWidth}`}
                      borderLeft
                    >
                      Expected
                    </CommonTableHeadingCell>

                    <CommonTableHeadingCell className={`${columnsWidth}`}>
                      Budget
                    </CommonTableHeadingCell>
                    <CommonTableHeadingCell
                      className={`${columnsWidth}`}
                      borderRight
                    >
                      Variance
                    </CommonTableHeadingCell>
                  </CommonTableBodyRow>

                  {regionSplitRowOrderTable?.map(({ label, key, unit }) => {
                    const item = feesMap[key];
                    // if (!item) return null;
                    return (
                      <CommonTableBodyRow key={label}>
                        <CommonTableBodyCell subHeading={false} borderRight>
                          <div className="text-left">{label}</div>
                        </CommonTableBodyCell>

                        <CommonTableBodyCell>
                          {formatValue(item?.Actual, unit)}
                        </CommonTableBodyCell>
                        <CommonTableBodyCell>
                          {formatValue(item?.budget, unit)}
                        </CommonTableBodyCell>

                        <CommonTableBodyCell borderRight>
                          {formatValue(item?.Variance, unit)}
                        </CommonTableBodyCell>
                        <CommonTableColumnSeperateCell />
                        <CommonTableBodyCell borderLeft>
                          {formatValue(item?.forecast, unit)}
                        </CommonTableBodyCell>
                        <CommonTableBodyCell>
                          {formatValue(item?.budget1, unit)}
                        </CommonTableBodyCell>
                        <CommonTableBodyCell borderRight>
                          {formatValue(item?.Variance1, unit)}
                        </CommonTableBodyCell>
                      </CommonTableBodyRow>
                    );
                  })}
                </React.Fragment>
              );
            })}
          </tbody>
        </CommonTable>
      </div>
    </>
  );
};

export default RegionWiseSplitTable;
