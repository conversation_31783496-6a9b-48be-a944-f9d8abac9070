import { useCallback } from 'react';
import {
  getGoogleReviews,
  getImages,
  getJTurnerScore,
  getPropertyStrategy,
  getScoreCard,
  getScoreCardFilterOptions,
  getSubmarketOccupancy,
} from '@/api/scorecardApi';
import {
  setError,
  setFinancialDateRange,
  setFinancialMetrics,
  setLoading,
  setOccupancyMetrics,
  setPerformanceRows,
  setPropertyInfo,
  setRawApiData,
  setRentalMetrics,
  setReputationMetrics,
  setSectionError,
  setSectionLoading,
  setSummaryTexts,
  setYtdTurnCost,
  type ErrorStates,
  type LoadingStates,
  type PropertyInfo as PropertyInfoType,
} from '@/slice/scoreCardSlice';
import dayjs from 'dayjs';
import { useDispatch } from 'react-redux';
import {
  FilterOptionsResponse,
  GoogleReviewsResponse,
  ImagesResponse,
  JTurnerScoreResponse,
  PropertyStrategyResponse,
  ScorecardAPIParams,
  ScoreCardResponse,
} from '../types/scorecardTypes';
import {
  generateSummaryText,
  getFinancialDateRange,
  transformFinancialMetrics,
  transformOccupancyMetrics,
  transformPerformanceRows,
  transformPropertyInfo,
  transformRentalMetrics,
  transformReputationMetrics,
  updatePropertyInfoWithScorecard,
} from '../utils/dataTransformers';
import { formatCurrencyForTurnCost } from '../utils/formatUtils';

export const useScorecardData = () => {
  const dispatch = useDispatch();

  const setSectionLoadingStates = useCallback(
    (loading: boolean) => {
      const sections: (keyof LoadingStates)[] = [
        'propertyInfo',
        'occupancyMetrics',
        'rentalMetrics',
        'financialMetrics',
        'performanceRows',
        'reputationMetrics',
        'summaryTexts',
      ];
      sections.forEach((section) => {
        dispatch(setSectionLoading({ section, loading }));
      });
    },
    [dispatch],
  );

  const clearSectionErrors = useCallback(() => {
    const sections: (keyof ErrorStates)[] = [
      'propertyInfo',
      'occupancyMetrics',
      'rentalMetrics',
      'financialMetrics',
      'performanceRows',
      'reputationMetrics',
      'summaryTexts',
    ];
    sections.forEach((section) => {
      dispatch(setSectionError({ section, error: null }));
    });
  }, [dispatch]);

  const fetchPropertyInfo = useCallback(
    async (
      propertyCode: string,
      startDate: string,
      endDate: string,
      filterOptionsResponse?: FilterOptionsResponse,
    ) => {
      try {
        const [propertyStrategyResponse, imagesResponse] = await Promise.all([
          getPropertyStrategy({ propertyCode, startDate, endDate }),
          getImages({ bu: parseInt(propertyCode) }),
        ]);

        if (filterOptionsResponse) {
          const propertyStrategy =
            propertyStrategyResponse?.data?.[0]?.Property_Strategy_This_Year ||
            '0';
          const sameStore =
            propertyStrategyResponse?.data?.[0]?.Same_Store || '0';

          // Use base64 image if available, fallback to URL
          const imageUrl = imagesResponse?.data?.[0]?.image_url;

          const propertyInfo = transformPropertyInfo(
            filterOptionsResponse,
            propertyCode,
            propertyStrategy,
            sameStore,
            imageUrl,
          );

          dispatch(setPropertyInfo(propertyInfo));
          dispatch(
            setSectionLoading({ section: 'propertyInfo', loading: false }),
          );

          return {
            propertyInfo,
            propertyStrategyResponse,
            imagesResponse,
          };
        } else {
          console.warn(
            'No filter options available for property info transformation',
          );
          dispatch(
            setSectionLoading({ section: 'propertyInfo', loading: false }),
          );
        }
      } catch (error) {
        console.error('Error fetching property info:', error);
        dispatch(
          setSectionError({
            section: 'propertyInfo',
            error: 'Failed to load property info',
          }),
        );
        dispatch(
          setSectionLoading({ section: 'propertyInfo', loading: false }),
        );
      }
      return null;
    },
    [dispatch],
  );

  const fetchScorecardMetrics = useCallback(
    async (params: ScorecardAPIParams, propertyUnits?: number) => {
      try {
        const scorecardResponse = await getScoreCard(params);

        const occupancyMetrics = transformOccupancyMetrics(
          scorecardResponse,
          undefined,
          propertyUnits,
        );
        const rentalMetrics = transformRentalMetrics(scorecardResponse);
        const financialMetrics = transformFinancialMetrics(scorecardResponse);
        const performanceRows = transformPerformanceRows(
          scorecardResponse,
          propertyUnits,
        );
        const financialDateRange = getFinancialDateRange(scorecardResponse);

        dispatch(setOccupancyMetrics(occupancyMetrics));
        dispatch(setRentalMetrics(rentalMetrics));
        dispatch(setFinancialMetrics(financialMetrics));
        dispatch(setPerformanceRows(performanceRows));
        dispatch(setFinancialDateRange(financialDateRange));
        dispatch(setRawApiData(scorecardResponse?.data || null));
        dispatch(
          setYtdTurnCost(
            formatCurrencyForTurnCost(scorecardResponse?.data?.COST_Per_Turn),
          ),
        );

        (
          [
            'occupancyMetrics',
            'rentalMetrics',
            'financialMetrics',
            'performanceRows',
          ] as const
        ).forEach((section) => {
          dispatch(setSectionLoading({ section, loading: false }));
        });

        return scorecardResponse;
      } catch (error) {
        console.error('Error fetching scorecard metrics:', error);
        (
          [
            'occupancyMetrics',
            'rentalMetrics',
            'financialMetrics',
            'performanceRows',
          ] as const
        ).forEach((section) => {
          dispatch(
            setSectionError({
              section,
              error: `Failed to load ${section}`,
            }),
          );
          dispatch(setSectionLoading({ section, loading: false }));
        });
      }
      return null;
    },
    [dispatch],
  );

  const generateSummary = useCallback(
    (
      propertyInfo: PropertyInfoType | null,
      scorecardResponse: ScoreCardResponse,
      submarketOccupancy?: number,
    ) => {
      try {
        const summaryTexts = generateSummaryText(
          propertyInfo?.propertyName || 'N/A',
          scorecardResponse?.data?.Occupancy_Trend || 0,
          submarketOccupancy || 0,
          scorecardResponse?.data?.gain_loss || 0,
          scorecardResponse?.data?.t30_show || 0,
          (scorecardResponse?.data?.Occupancy_Trendt30 || 0) -
            (scorecardResponse?.data?.Occupancy_Trend || 0),
          propertyInfo?.units,
        );

        dispatch(setSummaryTexts(summaryTexts));
        dispatch(
          setSectionLoading({ section: 'summaryTexts', loading: false }),
        );
      } catch (error) {
        console.error('Error generating summary:', error);
        dispatch(
          setSectionError({
            section: 'summaryTexts',
            error: 'Failed to generate summary',
          }),
        );
        dispatch(
          setSectionLoading({ section: 'summaryTexts', loading: false }),
        );
      }
    },
    [dispatch],
  );

  const fetchSubmarketOccupancy = useCallback(
    async (
      params: ScorecardAPIParams,
      propertyInfo: PropertyInfoType,
      scorecardResponse: ScoreCardResponse,
    ) => {
      if (!propertyInfo?.submarket || propertyInfo.submarket === '0') {
        generateSummary(propertyInfo, scorecardResponse, 0);
        return;
      }

      try {
        const submarketName = propertyInfo.submarket.split('-')[0].trim();
        const submarketResponse = await getSubmarketOccupancy({
          startDate: params.startDate,
          endDate: params.endDate,
          subMarketName: submarketName,
        });
        const submarketOccupancy =
          submarketResponse?.data?.[0]?.submarket_occupancy;

        const updatedOccupancyMetrics = transformOccupancyMetrics(
          scorecardResponse,
          submarketOccupancy,
          propertyInfo.units,
        );
        dispatch(setOccupancyMetrics(updatedOccupancyMetrics));

        generateSummary(propertyInfo, scorecardResponse, submarketOccupancy);
      } catch (error) {
        console.error('Error fetching submarket occupancy:', error);
        generateSummary(propertyInfo, scorecardResponse, 0);
      }
    },
    [dispatch, generateSummary],
  );

  const fetchReputationMetrics = useCallback(
    async (propertyCode: string) => {
      try {
        const [jTurnerResponse, googleReviewsResponse] = await Promise.all([
          getJTurnerScore({ propertyCode: parseInt(propertyCode) }),
          getGoogleReviews({ propertyCode: parseInt(propertyCode) }),
        ]);

        const reputationMetrics = transformReputationMetrics(
          jTurnerResponse as JTurnerScoreResponse,
          googleReviewsResponse as GoogleReviewsResponse,
        );

        dispatch(setReputationMetrics(reputationMetrics));
        dispatch(
          setSectionLoading({ section: 'reputationMetrics', loading: false }),
        );
      } catch (error) {
        console.error('Error fetching reputation metrics:', error);
        dispatch(
          setSectionError({
            section: 'reputationMetrics',
            error: 'Failed to load reputation metrics',
          }),
        );
        dispatch(
          setSectionLoading({ section: 'reputationMetrics', loading: false }),
        );
      }
    },
    [dispatch],
  );

  const fetchScorecardData = useCallback(
    async (
      params: ScorecardAPIParams,
      filterOptions?: FilterOptionsResponse,
    ) => {
      dispatch(setLoading(true));
      dispatch(setError(null));
      setSectionLoadingStates(true);
      clearSectionErrors();

      try {
        const currentFilterOptions = filterOptions;
        const [propertyInfoResult, scorecardResponse] =
          await Promise.allSettled([
            fetchPropertyInfo(
              params.propertyCode,
              params.startDate,
              params.endDate,
              currentFilterOptions,
            ),
            fetchScorecardMetrics(params),
            fetchReputationMetrics(params.propertyCode),
          ]);

        const propertyInfo =
          propertyInfoResult.status === 'fulfilled'
            ? propertyInfoResult.value?.propertyInfo
            : null;

        const scorecard =
          scorecardResponse.status === 'fulfilled'
            ? scorecardResponse.value
            : null;

        if (propertyInfo && scorecard) {
          const updatedPropertyInfo = updatePropertyInfoWithScorecard(
            propertyInfo,
            scorecard,
          );
          dispatch(setPropertyInfo(updatedPropertyInfo));

          const occupancyMetrics = transformOccupancyMetrics(
            scorecard,
            undefined,
            propertyInfo.units,
          );
          dispatch(setOccupancyMetrics(occupancyMetrics));

          await fetchSubmarketOccupancy(params, propertyInfo, scorecard);
        }
      } catch (error) {
        console.error('Error in fetchScorecardData:', error);
        dispatch(setError('Failed to load scorecard data'));
      } finally {
        dispatch(setLoading(false));
      }
    },
    [
      dispatch,
      setSectionLoadingStates,
      clearSectionErrors,
      fetchPropertyInfo,
      fetchScorecardMetrics,
      fetchReputationMetrics,
      fetchSubmarketOccupancy,
      generateSummary,
    ],
  );

  const fetchScorecardDataParallel = useCallback(
    async (
      params: ScorecardAPIParams,
      onFilterOptionsLoaded?: (filterOptions: FilterOptionsResponse) => void,
    ) => {
      dispatch(setLoading(true));
      dispatch(setError(null));
      setSectionLoadingStates(true);
      clearSectionErrors();

      try {
        const filterOptionsPromise = getScoreCardFilterOptions();
        const scorecardPromise = getScoreCard(params);
        const reputationPromise = Promise.all([
          getJTurnerScore({ propertyCode: parseInt(params.propertyCode) }),
          getGoogleReviews({ propertyCode: parseInt(params.propertyCode) }),
        ]);
        const propertyInfoPromises = Promise.all([
          getPropertyStrategy({
            propertyCode: params.propertyCode,
            startDate: params.startDate,
            endDate: params.endDate,
          }),
          getImages({ bu: parseInt(params.propertyCode) }),
        ]);

        let filterOptions: FilterOptionsResponse | null = null;
        let scorecardData: ScoreCardResponse | null = null;
        let propertyInfoData:
          | [PropertyStrategyResponse, ImagesResponse]
          | null = null;
        let transformedPropertyInfo: PropertyInfoType | null = null;

        const generateSummaryAndDependentOps = async (
          propertyInfo: PropertyInfoType,
          scorecard: ScoreCardResponse,
        ) => {
          const updatedPropertyInfo = updatePropertyInfoWithScorecard(
            propertyInfo,
            scorecard,
          );
          dispatch(setPropertyInfo(updatedPropertyInfo));

          if (
            propertyInfo.submarket &&
            propertyInfo.submarket !== '0' &&
            propertyInfo.submarket !== null &&
            propertyInfo.submarket !== undefined
          ) {
            try {
              const submarketName = propertyInfo.submarket.split('-')[0].trim();
              const submarketResponse = await getSubmarketOccupancy({
                startDate: params.startDate,
                endDate: params.endDate,
                subMarketName: submarketName,
              });
              const submarketOccupancy =
                submarketResponse?.data?.[0]?.submarket_occupancy;

              const updatedOccupancyMetrics = transformOccupancyMetrics(
                scorecard,
                submarketOccupancy,
                propertyInfo.units,
              );
              dispatch(setOccupancyMetrics(updatedOccupancyMetrics));

              generateSummary(propertyInfo, scorecard, submarketOccupancy);
            } catch (error) {
              console.error('Error fetching submarket occupancy:', error);
              generateSummary(propertyInfo, scorecard, 0);
            }
          } else {
            generateSummary(propertyInfo, scorecard, 0);
          }
        };

        filterOptionsPromise
          .then((response) => {
            filterOptions = response;
            if (onFilterOptionsLoaded) {
              onFilterOptionsLoaded(response);
            }

            if (propertyInfoData) {
              const propertyStrategy =
                propertyInfoData[0]?.data?.[0]?.Property_Strategy_This_Year ||
                '0';
              const sameStore =
                propertyInfoData[0]?.data?.[0]?.Same_Store || '0';
              const imageUrl = propertyInfoData[1]?.data?.[0]?.image_url;

              transformedPropertyInfo = transformPropertyInfo(
                response,
                params.propertyCode,
                propertyStrategy,
                sameStore,
                imageUrl,
              );

              dispatch(setPropertyInfo(transformedPropertyInfo));
              dispatch(
                setSectionLoading({ section: 'propertyInfo', loading: false }),
              );
            }
          })
          .catch((error) => {
            console.error('Error loading filter options:', error);
          });

        scorecardPromise
          .then((response) => {
            scorecardData = response;

            const occupancyMetrics = transformOccupancyMetrics(
              response,
              undefined,
              transformedPropertyInfo?.units,
            );
            const rentalMetrics = transformRentalMetrics(response);
            const financialMetrics = transformFinancialMetrics(response);
            const performanceRows = transformPerformanceRows(
              response,
              transformedPropertyInfo?.units,
            );
            const financialDateRange = getFinancialDateRange(response);

            dispatch(setOccupancyMetrics(occupancyMetrics));
            dispatch(setRentalMetrics(rentalMetrics));
            dispatch(setFinancialMetrics(financialMetrics));
            dispatch(setPerformanceRows(performanceRows));
            dispatch(setFinancialDateRange(financialDateRange));
            dispatch(setRawApiData(response?.data || null));
            dispatch(
              setYtdTurnCost(
                formatCurrencyForTurnCost(response?.data?.COST_Per_Turn),
              ),
            );

            (
              [
                'occupancyMetrics',
                'rentalMetrics',
                'financialMetrics',
                'performanceRows',
              ] as const
            ).forEach((section) => {
              dispatch(setSectionLoading({ section, loading: false }));
            });
          })
          .catch((error) => {
            console.error('Error fetching scorecard metrics:', error);
            (
              [
                'occupancyMetrics',
                'rentalMetrics',
                'financialMetrics',
                'performanceRows',
              ] as const
            ).forEach((section) => {
              dispatch(
                setSectionError({
                  section,
                  error: `Failed to load ${section}`,
                }),
              );
              dispatch(setSectionLoading({ section, loading: false }));
            });
          });

        reputationPromise
          .then(([jTurnerResponse, googleReviewsResponse]) => {
            const reputationMetrics = transformReputationMetrics(
              jTurnerResponse as JTurnerScoreResponse,
              googleReviewsResponse as GoogleReviewsResponse,
            );

            dispatch(setReputationMetrics(reputationMetrics));
            dispatch(
              setSectionLoading({
                section: 'reputationMetrics',
                loading: false,
              }),
            );
          })
          .catch((error) => {
            console.error('Error fetching reputation metrics:', error);
            dispatch(
              setSectionError({
                section: 'reputationMetrics',
                error: 'Failed to load reputation metrics',
              }),
            );
            dispatch(
              setSectionLoading({
                section: 'reputationMetrics',
                loading: false,
              }),
            );
          });

        propertyInfoPromises
          .then((responses) => {
            propertyInfoData = responses;

            if (filterOptions) {
              const propertyStrategy =
                responses[0]?.data?.[0]?.Property_Strategy_This_Year || '0';
              const sameStore = responses[0]?.data?.[0]?.Same_Store || '0';
              const imageUrl = responses[1]?.data?.[0]?.image_url;

              transformedPropertyInfo = transformPropertyInfo(
                filterOptions,
                params.propertyCode,
                propertyStrategy,
                sameStore,
                imageUrl,
              );

              dispatch(setPropertyInfo(transformedPropertyInfo));
              dispatch(
                setSectionLoading({ section: 'propertyInfo', loading: false }),
              );
            }
          })
          .catch((error) => {
            console.error('Error fetching property info:', error);
            dispatch(
              setSectionError({
                section: 'propertyInfo',
                error: 'Failed to load property info',
              }),
            );
            dispatch(
              setSectionLoading({ section: 'propertyInfo', loading: false }),
            );
          });

        await Promise.allSettled([
          filterOptionsPromise,
          scorecardPromise,
          reputationPromise,
          propertyInfoPromises,
        ]);

        // Generate summary after all promises have settled
        if (transformedPropertyInfo && scorecardData) {
          try {
            await generateSummaryAndDependentOps(
              transformedPropertyInfo,
              scorecardData,
            );
          } catch (error) {
            console.error('Error generating summary:', error);
            dispatch(
              setSectionError({
                section: 'summaryTexts',
                error: 'Failed to generate summary',
              }),
            );
            dispatch(
              setSectionLoading({ section: 'summaryTexts', loading: false }),
            );
          }
        } else {
          // Fallback: set summary loading to false if we couldn't generate summary
          console.warn('Unable to generate summary: missing required data', {
            hasPropertyInfo: !!transformedPropertyInfo,
            hasScorecardData: !!scorecardData,
          });
          dispatch(
            setSectionLoading({ section: 'summaryTexts', loading: false }),
          );
        }
      } catch (error) {
        console.error('Error in fetchScorecardDataParallel:', error);
        dispatch(setError('Failed to load scorecard data'));
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch, setSectionLoadingStates, clearSectionErrors, generateSummary],
  );

  const loadInitialData = useCallback(
    async (filterOptionsResponse?: FilterOptionsResponse) => {
      // Create 7-day range ending on today's date
      const endDate = dayjs();
      const startDate = endDate.subtract(7, 'day');

      const defaultParams: ScorecardAPIParams = {
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        propertyCode: '22514',
      };

      if (filterOptionsResponse) {
        await fetchScorecardData(defaultParams, filterOptionsResponse);
      } else {
        try {
          const filterOptions = await getScoreCardFilterOptions();
          await fetchScorecardData(defaultParams, filterOptions);
        } catch (error) {
          console.error('Error loading initial data:', error);
          await fetchScorecardData(defaultParams);
        }
      }
    },
    [fetchScorecardData],
  );

  const loadInitialDataParallel = useCallback(
    async (
      onFilterOptionsLoaded?: (filterOptions: FilterOptionsResponse) => void,
    ) => {
      // Create 7-day range ending on today's date
      const endDate = dayjs();
      const startDate = endDate.subtract(7, 'day');

      const defaultParams: ScorecardAPIParams = {
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        propertyCode: '22514',
      };

      await fetchScorecardDataParallel(defaultParams, onFilterOptionsLoaded);
    },
    [fetchScorecardDataParallel],
  );

  return {
    fetchScorecardData,
    fetchScorecardDataParallel,
    loadInitialData,
    loadInitialDataParallel,
  };
};
