import { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { RESPONSIVE_FONTS, FONT_SIZES } from '../constants/typography';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ScorecardErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Scorecard error:', error, errorInfo);
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: null });
    window.location.reload();
  };

  public render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-50">
          <div className="max-w-md w-full p-6 bg-white rounded-lg shadow-lg">
            <div className="flex items-center space-x-3 text-red-600 mb-4">
              <AlertCircle className="h-8 w-8" />
              <h2 className={`${RESPONSIVE_FONTS.errorMessage} font-semibold`}>Something went wrong</h2>
            </div>
            
            <p className="text-gray-600 mb-4">
              We encountered an error while loading the scorecard. This might be temporary.
            </p>
            
            {this.state.error && (
              <div className={`mb-4 p-3 bg-gray-100 rounded ${FONT_SIZES.xs} text-gray-700 font-mono`}>
                {this.state.error.message}
              </div>
            )}
            
            <div className="flex space-x-3">
              <Button
                onClick={this.handleReset}
                className="flex items-center space-x-2"
                style={{ backgroundColor: '#43298F' }}
              >
                <RefreshCw className="h-4 w-4" />
                <span>Reload Page</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.history.back()}
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}