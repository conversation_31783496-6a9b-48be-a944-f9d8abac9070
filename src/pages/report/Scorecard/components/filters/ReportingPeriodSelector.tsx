import React, { useCallback } from 'react';
import { DatePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { CSS_FONT_SIZES, FONT_SIZES } from '../../constants/typography';

interface ReportingPeriodSelectorProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (date: Dayjs | null) => void;
  onEndDateChange: (date: Dayjs | null) => void;
  minDate: Dayjs | null;
  maxDate: Dayjs | null;
  availableDates: Array<{
    label: string;
    value: string;
    dayjsValue?: Dayjs;
    monthKey?: string;
  }>;
}

export const ReportingPeriodSelector: React.FC<
  ReportingPeriodSelectorProps
> = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  minDate,
  maxDate,
  availableDates,
}) => {
  const convertISOToDayjs = (isoString: string): Dayjs | null => {
    if (!isoString) return null;
    return dayjs(isoString);
  };

  const getAvailableMonthKeys = useCallback(() => {
    return availableDates.map((d) => d.monthKey).filter(Boolean);
  }, [availableDates]);

  return (
    <div className="flex items-center space-x-1 flex-shrink-0">
      <span className="text-gray-600 hidden xl:inline text-xs">Dates:</span>
      <div className="flex items-center space-x-1">
        <DatePicker
          value={startDate ? convertISOToDayjs(startDate) : null}
          onChange={onStartDateChange}
          minDate={minDate || dayjs('2020-01-01')}
          maxDate={maxDate || dayjs('2030-12-31')}
          disabledDate={(current) => {
            if (!current || availableDates.length === 0) {
              return false;
            }
            const currentMonthKey = current.format('YYYY-MM');
            const availableMonthKeys = getAvailableMonthKeys();
            return !availableMonthKeys.includes(currentMonthKey);
          }}
          format="MM/DD/YYYY"
          className="w-20 sm:w-22 lg:w-24"
          style={{
            height: '28px',
            fontSize: CSS_FONT_SIZES.tiny,
          }}
          placeholder="Start"
        />
        <span className={`text-gray-400 ${FONT_SIZES.tiny} px-1`}>-</span>
        <DatePicker
          value={endDate ? convertISOToDayjs(endDate) : null}
          onChange={onEndDateChange}
          minDate={minDate || dayjs('2020-01-01')}
          maxDate={maxDate || dayjs('2030-12-31')}
          disabledDate={(current) => {
            if (!current || availableDates.length === 0) return false;

            const currentMonthKey = current.format('YYYY-MM');
            const availableMonthKeys = getAvailableMonthKeys();

            const isAvailable = availableMonthKeys.includes(currentMonthKey);

            const startDateValue = startDate ? dayjs(startDate) : null;
            const isAfterStart =
              !startDateValue ||
              current.isAfter(startDateValue) ||
              current.isSame(startDateValue, 'day');

            return !isAvailable || !isAfterStart;
          }}
          format="MM/DD/YYYY"
          className="w-20 sm:w-22 lg:w-24"
          style={{
            height: '28px',
            fontSize: CSS_FONT_SIZES.tiny,
          }}
          placeholder="End"
        />
      </div>
    </div>
  );
};
