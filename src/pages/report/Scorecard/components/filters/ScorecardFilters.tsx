import React, { useEffect, useRef, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { RESPONSIVE_FONTS } from '../../constants/typography';
import { FilterActions } from './FilterActions';
import { PropertySelector } from './PropertySelector';
import { ReportingPeriodSelector } from './ReportingPeriodSelector';
import { SystemSelector } from './SystemSelector';

export interface FilterValues {
  system: string;
  property: string;
  startDate: string;
  endDate: string;
  reportingPeriod: string;
}

interface ScorecardFiltersProps {
  filters: FilterValues;
  availableSystems: Array<{ label: string; value: string }>;
  availableProperties: Array<{ label: string; value: string }>;
  availableDates: Array<{
    label: string;
    value: string;
    dayjsValue?: Dayjs;
    monthKey?: string;
  }>;
  minDate: Dayjs | null;
  maxDate: Dayjs | null;
  onApplyFilters: (filters: FilterValues) => void;
  onExportPDF?: () => void;
  canExportPDF?: boolean;
  exportStatus?: string;
  isExporting?: boolean;
  rawSystemsData?: Array<{
    System: string;
    BU: string;
    property_hmy: string;
    PropertyName: string;
  }>;
}

export const ScorecardFilters: React.FC<ScorecardFiltersProps> = ({
  filters,
  availableSystems,
  availableProperties,
  availableDates,
  minDate,
  maxDate,
  onApplyFilters,
  onExportPDF,
  canExportPDF = false,
  exportStatus = 'Export PDF',
  isExporting = false,
  rawSystemsData = [],
}) => {
  const getDefaultFilters = (): FilterValues => {
    // Create 7-day range ending on today's date
    const endDate = dayjs();
    const startDate = endDate.subtract(7, 'day');

    return {
      system: 'N/A',
      property: '22514 - The Katy',
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
      reportingPeriod: `${startDate.format('M/D/YYYY')} - ${endDate.format('M/D/YYYY')}`,
    };
  };

  const areFiltersEmpty = (filters: FilterValues): boolean => {
    return (
      !filters.system &&
      !filters.property &&
      !filters.startDate &&
      !filters.endDate
    );
  };

  const [localFilters, setLocalFilters] = useState(() =>
    areFiltersEmpty(filters) ? getDefaultFilters() : filters,
  );

  const previousPropertyRef = useRef<string>('');
  const previousSystemRef = useRef<string>('');

  useEffect(() => {
    if (!areFiltersEmpty(filters)) {
      setLocalFilters(filters);
      previousPropertyRef.current = filters.property;
      previousSystemRef.current = filters.system;
    }
  }, [filters]);

  useEffect(() => {
    const performAutoSelection = () => {
      if (localFilters.property && rawSystemsData.length > 0) {
        const selectedPropertyBU = localFilters.property.split(' - ')[0];

        const matchingSystems = rawSystemsData.filter(
          (system) => system.BU === selectedPropertyBU,
        );

        if (matchingSystems.length > 0) {
          const targetSystem = matchingSystems[0].System;

          if (localFilters.system !== targetSystem) {
            setLocalFilters((prev) => ({ ...prev, system: targetSystem }));
          }
        } else {
          if (localFilters.system !== '') {
            setLocalFilters((prev) => ({ ...prev, system: '' }));
          }
        }
      } else if (!localFilters.property && localFilters.system !== '') {
        setLocalFilters((prev) => ({ ...prev, system: '' }));
      }
    };

    const propertyChanged =
      localFilters.property !== previousPropertyRef.current;
    const systemsDataAvailable = rawSystemsData.length > 0;
    const hasPropertyButNoSystem =
      localFilters.property && !localFilters.system;

    console.log('Auto-selection conditions:', {
      propertyChanged,
      systemsDataAvailable,
      hasPropertyButNoSystem,
    });

    if (propertyChanged || (systemsDataAvailable && hasPropertyButNoSystem)) {
      console.log('Running auto-selection...');
      performAutoSelection();
    }

    previousPropertyRef.current = localFilters.property;
    previousSystemRef.current = localFilters.system;
  }, [localFilters.property, localFilters.system, rawSystemsData]);

  const handleFilterChange = (filterType: string, value: string) => {
    setLocalFilters((prev) => ({ ...prev, [filterType]: value }));
  };

  const handleDateChange = (
    filterType: 'startDate' | 'endDate',
    dayjsValue: Dayjs | null,
  ) => {
    const dateValue = dayjsValue ? dayjsValue.format('YYYY-MM-DD') : '';
    handleFilterChange(filterType, dateValue);
  };

  const handleApply = () => {
    onApplyFilters(localFilters);
  };

  const handleReset = () => {
    setLocalFilters(getDefaultFilters());
  };

  const hasChanges = JSON.stringify(localFilters) !== JSON.stringify(filters);

  return (
    <div
      className={`flex items-center gap-1 sm:gap-2 ${RESPONSIVE_FONTS.filterLabel} flex-1 justify-end flex-nowrap`}
    >
      <SystemSelector
        value={localFilters.system}
        onChange={(value) => handleFilterChange('system', value)}
        options={availableSystems}
      />

      <PropertySelector
        value={localFilters.property}
        onChange={(value) => handleFilterChange('property', value)}
        options={availableProperties}
      />

      <ReportingPeriodSelector
        startDate={localFilters.startDate}
        endDate={localFilters.endDate}
        onStartDateChange={(date) => handleDateChange('startDate', date)}
        onEndDateChange={(date) => handleDateChange('endDate', date)}
        minDate={minDate}
        maxDate={maxDate}
        availableDates={availableDates}
      />

      <FilterActions
        hasChanges={hasChanges}
        onApply={handleApply}
        onReset={handleReset}
        onExportPDF={onExportPDF}
        canExportPDF={canExportPDF}
        exportStatus={exportStatus}
        isExporting={isExporting}
      />
    </div>
  );
};
