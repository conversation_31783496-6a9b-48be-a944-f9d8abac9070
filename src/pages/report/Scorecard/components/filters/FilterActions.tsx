import React from 'react';
import { FileDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FilterActionsProps {
  hasChanges: boolean;
  onApply: () => void;
  onReset: () => void;
  onExportPDF?: () => void;
  canExportPDF?: boolean;
  exportStatus?: string;
  isExporting?: boolean;
}

export const FilterActions: React.FC<FilterActionsProps> = ({
  hasChanges,
  onApply,
  onReset,
  onExportPDF,
  canExportPDF = false,
  exportStatus = 'Export PDF',
  isExporting = false,
}) => {
  return (
    <div className="flex items-center gap-1 flex-shrink-0">
      <Button
        onClick={onApply}
        disabled={!hasChanges}
        className="px-2 py-1 text-white font-medium rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-xs h-7"
        style={{
          backgroundColor: '#43298F',
          opacity: !hasChanges ? 0.5 : 1,
        }}
      >
        <span className="hidden sm:inline">Apply</span>
        <span className="sm:hidden">✓</span>
      </Button>
      <Button
        onClick={onReset}
        variant="outline"
        className="px-2 py-1 font-medium rounded-md border-2 transition-all duration-200 text-xs h-7"
        style={{
          color: '#43298F',
          borderColor: '#43298F',
          backgroundColor: 'white',
        }}
      >
        <span className="hidden sm:inline">Reset</span>
        <span className="sm:hidden">↺</span>
      </Button>
      {onExportPDF && (
        <Button
          onClick={onExportPDF}
          disabled={!canExportPDF || isExporting}
          variant="outline"
          className="px-2 py-1 font-medium rounded-md border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-xs h-7"
          style={{
            color: '#43298F',
            borderColor: '#43298F',
            backgroundColor: 'white',
            opacity: !canExportPDF || isExporting ? 0.5 : 1,
          }}
        >
          <FileDown
            className={`w-3 h-3 mr-1 ${isExporting ? 'animate-spin' : ''}`}
          />
          <span className="hidden sm:inline">
            {exportStatus === 'Export PDF' ? 'PDF' : exportStatus}
          </span>
        </Button>
      )}
    </div>
  );
};
