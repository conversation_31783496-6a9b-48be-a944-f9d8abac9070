import React from 'react';
import { RentalMetric } from '@/slice/scoreCardSlice';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { getTextColorClass } from '../utils/colorUtils';
import LoadingSkeleton from './shared/LoadingSkeleton';
import { RESPONSIVE_FONTS } from '../constants/typography';

const RentalMetrics: React.FC = () => {
  const { rentalMetrics, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.rentalMetrics;

  const renderPairedCard = (
    metric1: RentalMetric,
    metric2: RentalMetric,
    key: string,
  ) => (
    <div key={key} className="py-1 sm:py-2 px-1 bg-white flex-1 h-full">
      <div className="flex items-stretch h-full">
        <div className="flex-1 text-center flex flex-col justify-center px-1 min-w-0">
          <div className="flex flex-col items-center">
            <div
              className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric1?.color)}`}
            >
              {metric1?.value}
            </div>
            <div className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}>
              {metric1?.label}
            </div>
          </div>
        </div>
        <div className="w-px bg-gray-300 mx-1 sm:mx-2 self-stretch"></div>
        <div className="flex-1 text-center flex flex-col justify-center px-1 min-w-0">
          <div className="flex flex-col items-center">
            <div
              className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric2?.color)}`}
            >
              {metric2?.value}
            </div>
            <div className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}>
              {metric2?.label}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="h-full flex flex-col">
        <div
          className="p-1 sm:p-2 flex-shrink-0"
          style={{ backgroundColor: '#8B8FE8' }}
        >
          <div className="h-4 bg-gray-300 rounded w-64 animate-pulse"></div>
        </div>
        <div className="mt-1 sm:mt-2 flex-1">
          <LoadingSkeleton type="metrics-grid" columns={4} className="h-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div
        className="p-1 sm:p-2 flex-shrink-0"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        <h3 className={`font-medium text-white ${RESPONSIVE_FONTS.sectionHeader}`}>
          Net In Place Effective Rents, Year Over Year Change, Renewal
          Conversion
        </h3>
      </div>

      {/* Metrics */}
      <div className="flex justify-between gap-1 sm:gap-2 mt-1 sm:mt-2 flex-1">
        {/* Group all metrics in pairs */}
        {Array.from(
          { length: Math.ceil(rentalMetrics.length / 2) },
          (_, pairIndex) => {
            const startIndex = pairIndex * 2;
            const metric1 = rentalMetrics[startIndex];
            const metric2 = rentalMetrics[startIndex + 1];

            if (metric2) {
              return renderPairedCard(metric1, metric2, `pair-${pairIndex}`);
            } else {
              return (
                <div
                  key={`single-${pairIndex}`}
                  className="py-1 sm:py-2 px-1 bg-white flex-1 text-center h-full flex flex-col justify-center min-w-0"
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric1?.color)}`}
                    >
                      {metric1?.value}
                    </div>
                    <div className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}>
                      {metric1?.label}
                    </div>
                  </div>
                </div>
              );
            }
          },
        )}
      </div>
    </div>
  );
};

export default RentalMetrics;
