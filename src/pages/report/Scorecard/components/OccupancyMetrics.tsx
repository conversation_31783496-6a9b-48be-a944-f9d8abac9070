import React from 'react';
import { OccupancyMetric } from '@/slice/scoreCardSlice';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { RESPONSIVE_FONTS } from '../constants/typography';
import {
  getMetricBackgroundColor,
  getTextColorClass,
} from '../utils/colorUtils';
import LoadingSkeleton from './shared/LoadingSkeleton';

const OccupancyMetrics: React.FC = () => {
  const { occupancyMetrics, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.occupancyMetrics;

  const renderGroupCard = (
    groupMetrics: OccupancyMetric[],
    key: string,
    flexBasis: string,
  ) => {
    const backgroundColor =
      getMetricBackgroundColor(groupMetrics[0].label) || 'white';

    return (
      <div
        key={key}
        className="py-1 sm:py-2 px-1 h-full"
        style={{ backgroundColor, flexBasis }}
      >
        <div className="flex items-stretch h-full">
          {groupMetrics.map((metric, index) => (
            <React.Fragment key={index}>
              <div className="flex-1 text-center flex flex-col justify-center px-1 min-w-0">
                <div className="flex flex-col items-center">
                  <div
                    className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric.color)}`}
                  >
                    {metric.value}
                  </div>
                  <div
                    className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}
                  >
                    {metric.label}
                  </div>
                </div>
              </div>
              {index < groupMetrics.length - 1 && (
                <div className="w-px bg-gray-300 mx-1 sm:mx-2 self-stretch"></div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  const getGroupedMetrics = () => {
    const groups = [
      { start: 0, end: 2 },
      { start: 3, end: 6 },
      { start: 7, end: 7 },
      { start: 8, end: 11 },
      { start: 12, end: 12 },
    ];

    const totalMetrics = occupancyMetrics.length;

    return groups.map((group, groupIndex) => {
      const groupMetrics = occupancyMetrics.slice(group.start, group.end + 1);
      const metricsCount = groupMetrics.length;
      const flexBasis = `${(metricsCount / totalMetrics) * 100}%`;

      return renderGroupCard(groupMetrics, `group-${groupIndex}`, flexBasis);
    });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div
          className="p-1 sm:p-2 flex-shrink-0"
          style={{ backgroundColor: '#8B8FE8' }}
        >
          <div className="h-4 bg-gray-300 w-32 animate-pulse"></div>
        </div>
        <div className="mt-1 sm:mt-2 flex-1">
          <LoadingSkeleton type="metrics-grid" columns={5} className="h-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div
        className="p-1 sm:p-2 flex-shrink-0"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        <h3
          className={`font-medium text-white ${RESPONSIVE_FONTS.sectionHeader}`}
        >
          Occupancy & Leasing
        </h3>
      </div>

      {/* Metrics */}
      <div className="flex justify-between gap-1 sm:gap-2 mt-1 sm:mt-2 flex-1">
        {getGroupedMetrics()}
      </div>
    </div>
  );
};

export default OccupancyMetrics;
