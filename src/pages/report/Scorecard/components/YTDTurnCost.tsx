import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import LoadingSkeleton from './shared/LoadingSkeleton';
import MetricCard from './shared/MetricCard';
import { RESPONSIVE_FONTS } from '../constants/typography';

const YTDTurnCost: React.FC = () => {
  const { ytdTurnCost, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.financialMetrics;

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div
          className="p-1 sm:p-2 flex-shrink-0"
          style={{ backgroundColor: '#8B8FE8' }}
        >
          <div className="h-4 bg-gray-300 w-16 animate-pulse"></div>
        </div>
        <div className="mt-1 sm:mt-2 flex-1">
          <LoadingSkeleton type="metric-card" className="h-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div
        className="p-1 sm:p-2 flex-shrink-0"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        <h3 className={`font-medium text-white ${RESPONSIVE_FONTS.sectionHeader}`}>
          YTD Turn Cost
        </h3>
      </div>
      <div className="mt-1 sm:mt-2 flex-1">
        <MetricCard
          value={ytdTurnCost}
          label="AVG COST PER TURN"
          className="bg-white h-full"
          valueClassName="text-gray-900"
        />
      </div>
    </div>
  );
};

export default YTDTurnCost;
