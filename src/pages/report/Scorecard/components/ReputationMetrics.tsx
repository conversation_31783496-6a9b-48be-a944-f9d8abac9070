import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import RatingDisplay from './shared/RatingDisplay';
import { FONT_SIZES } from '../constants/typography';

const ReputationMetrics: React.FC = () => {
  const { reputationMetrics, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.reputationMetrics;

  if (isLoading) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-sm animate-pulse">
        <div className="h-5 bg-gray-300 rounded w-40 mb-4"></div>
        <div className="space-y-4">
          {[...Array(2)].map((_, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="h-4 bg-gray-300 rounded w-32"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <h3 className={`${FONT_SIZES.sm} font-semibold text-gray-900 mb-4`}>
        Reputation Management
      </h3>
      <div className="space-y-4">
        {reputationMetrics.map((metric, index) => (
          <div key={index} className="flex items-center justify-between">
            <RatingDisplay
              platform={metric.platform}
              rating={metric.rating}
              maxRating={metric.maxRating}
              size="large"
              progressBarColor="#9333ea"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReputationMetrics;
