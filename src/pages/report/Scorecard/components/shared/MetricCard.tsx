import React from 'react';
import { getTextColorClass, type MetricColor } from '../../utils/colorUtils';
import { RESPONSIVE_FONTS } from '../../constants/typography';

interface MetricCardProps {
  value: string | number;
  label: string;
  color?: MetricColor | string;
  backgroundColor?: string;
  className?: string;
  valueClassName?: string;
  labelClassName?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  value,
  label,
  color,
  backgroundColor = 'white',
  className = '',
  valueClassName = '',
  labelClassName = '',
}) => {
  const textColor = getTextColorClass(color);

  return (
    <div
      className={`py-1 sm:py-2 px-1 h-full ${className}`}
      style={{ backgroundColor }}
    >
      <div className="text-center flex flex-col justify-center px-1 h-full min-w-0">
        <div className="flex flex-col items-center">
          <div
            className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${textColor} ${valueClassName}`}
          >
            {value}
          </div>
          <div
            className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem] ${labelClassName}`}
          >
            {label}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MetricCard;
