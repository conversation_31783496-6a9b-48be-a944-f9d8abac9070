import dayjs from 'dayjs';

export const PAGE_BG = '#F4F4FF';
export const HEADER_COLOR = '#432890';
export const BUTTON_COLOR = '#43298F';

// Create 7-day range ending on today's date
const endDate = dayjs();
const startDate = endDate.subtract(7, 'day');
const defaultStartDate = startDate.format('YYYY-MM-DD');
const defaultEndDate = endDate.format('YYYY-MM-DD');

export const DEFAULT_FILTERS = {
  system: 'N/A',
  property: '22514 - The Katy',
  startDate: defaultStartDate,
  endDate: defaultEndDate,
  reportingPeriod: `${startDate.format('M/D/YYYY')} - ${endDate.format('M/D/YYYY')}`,
};

export const DEFAULT_API_PARAMS = {
  startDate: defaultStartDate,
  endDate: defaultEndDate,
  propertyCode: '22514',
};

export const SECTION_HEIGHTS = {
  metrics: '60%',
  performance: '40%',
};

export const PERFORMANCE_TABLE_FLEX = 4;
export const SUMMARY_SECTION_FLEX = 1;
