import axios from 'axios';

const URL = import.meta.env.VITE_API_URL;

interface ScoreCardRequest {
  startDate: string; // YYYY-MM-DD format
  endDate: string; // YYYY-MM-DD format
  propertyCode: string;
}

interface ScoreCardResponse {
  data: {
    Collect_recovery_ratio: number;
    outstanding_tickets: number;
    vcnt_units: number;
    avg_vcnt_days: number;
    aged_vacant_units: number;
    Occupancy_Non_Rev: number;
    Occupancy_Trend: number;
    gain_loss: number;
    units_available: number;
    t30_show: number;
    In_Place_rent: number;
    'In_Place_rent/sqft': number;
    Prev_Yr_In_Place_rent: number;
    New_In_Place_rent: number;
    'New_In_Place_rent/sqft': number;
    Prev_Yr_New_In_Place_rent: number;
    Renewal_In_Place_rent: number;
    'Renewal_In_Place_rent/sqft': number;
    Prev_Yr_Renewal_In_Place_rent: number;
    MTM: number;
    Income: number;
    Controllable_Opex: number;
    total_OPex: number;
    NOI: number;
    Controll_NOI: number;
    Capital: number;
    COST_Per_Turn: number;
    collection_MTD: number;
    capital_execution: number;
    avg_turn_time: number;
    repeat_tickets: number;
    Residential_sqft: number;
    AVG_Residential_sqft: number;
    Retail_sqft: number;
    Retail_Spaces: number;
    Affordable: number;
    Non_Revenue: number;
    Down: number;
    Occupancy_Trendt30: number;
    YTD_Renewal_Conversion: number;
    'bad_debt_w/o_%_GRI': number;
    Adjusted_Period_Start_Month: number;
    Adjusted_Period_Start_Day: number;
    Adjusted_Period_Start_Year: number;
    Adjusted_Period_End_Month: number;
    Adjusted_Period_End_Day: number;
    Adjusted_Period_End_Year: number;
  };
}

export const getScoreCard = async (body: ScoreCardRequest) => {
  try {
    const response = await axios.post<ScoreCardResponse>(
      `${URL}/score-card`,
      body,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching score card:', error);
    return {
      data: {
        Collect_recovery_ratio: 0,
        outstanding_tickets: 0,
        vcnt_units: 0,
        avg_vcnt_days: 0,
        aged_vacant_units: 0,
        Occupancy_Non_Rev: 0,
        Occupancy_Trend: 0,
        gain_loss: 0,
        units_available: 0,
        t30_show: 0,
        In_Place_rent: 0,
        'In_Place_rent/sqft': 0,
        Prev_Yr_In_Place_rent: 0,
        New_In_Place_rent: 0,
        'New_In_Place_rent/sqft': 0,
        Prev_Yr_New_In_Place_rent: 0,
        Renewal_In_Place_rent: 0,
        'Renewal_In_Place_rent/sqft': 0,
        Prev_Yr_Renewal_In_Place_rent: 0,
        MTM: 0,
        Income: 0,
        Controllable_Opex: 0,
        total_OPex: 0,
        NOI: 0,
        Controll_NOI: 0,
        Capital: 0,
        COST_Per_Turn: 0,
        collection_MTD: 0,
        capital_execution: 0,
        avg_turn_time: 0,
        repeat_tickets: 0,
        Residential_sqft: 0,
        AVG_Residential_sqft: 0,
        Retail_sqft: 0,
        Retail_Spaces: 0,
        Affordable: 0,
        Non_Revenue: 0,
        Down: 0,
        Occupancy_Trendt30: 0,
        YTD_Renewal_Conversion: 0,
        'bad_debt_w/o_%_GRI': 0,
        Adjusted_Period_Start_Month: 0,
        Adjusted_Period_Start_Day: 0,
        Adjusted_Period_Start_Year: 0,
        Adjusted_Period_End_Month: 0,
        Adjusted_Period_End_Day: 0,
        Adjusted_Period_End_Year: 0,
      },
    };
  }
};

interface PropertyStrategyRequest {
  startDate: string; // YYYY-MM-DD format
  endDate: string; // YYYY-MM-DD format
  propertyCode: string;
}

interface PropertyStrategyResponse {
  data: Array<{
    Property_Strategy_This_Year: string;
    Same_Store: string;
  }>;
}

export const getPropertyStrategy = async (body: PropertyStrategyRequest) => {
  try {
    const response = await axios.post<PropertyStrategyResponse>(
      `${URL}/property-strategy`,
      body,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching property strategy:', error);
    return {
      data: [],
    };
  }
};

interface ImagesRequest {
  bu: number;
}

interface ImagesResponse {
  data: Array<{
    BU: string;
    Property: string;
    name: string;
    placeid: string;
    image_url: string;
    image_base64?: string;
    image_error?: string;
  }>;
}

export const getImages = async (body: ImagesRequest) => {
  try {
    const response = await axios.post<ImagesResponse>(`${URL}/images`, body);
    return response.data;
  } catch (error) {
    console.error('Error fetching images:', error);
    return {
      data: [],
    };
  }
};

// export const getImagesBase64 = async (body: ImagesRequest) => {
//   try {
//     const response = await axios.post<ImagesResponse>(
//       `${URL}/images-base64`,
//       body,
//     );
//     return response.data;
//   } catch (error) {
//     console.error('Error fetching images with base64:', error);
//     return {
//       data: [],
//     };
//   }
// };

interface JTurnerScoreRequest {
  propertyCode: number;
}

interface JTurnerScoreResponse {
  data: Array<{
    client_id: string;
    property_name: string;
    ora_score: string;
    national_ora_score: string;
    company_ora_score: string;
  }>;
}

export const getJTurnerScore = async (body: JTurnerScoreRequest) => {
  try {
    const response = await axios.post<JTurnerScoreResponse>(
      `${URL}/jturner-score`,
      body,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching J-Turner score:', error);
    return {
      data: [],
    };
  }
};

interface GoogleReviewsRequest {
  propertyCode: number;
}

interface GoogleReviewsResponse {
  data: Array<{
    propertyBu: string;
    title: string;
    placeid: string;
    rating: string;
    addressLines: string;
  }>;
}

export const getGoogleReviews = async (body: GoogleReviewsRequest) => {
  try {
    const response = await axios.post<GoogleReviewsResponse>(
      `${URL}/google-reviews`,
      body,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching Google reviews:', error);
    return {
      data: [],
    };
  }
};

type FilterOptionsRequest = object;

interface FilterOptionsResponse {
  data: {
    properties: Array<{
      property_sk: number;
      Property_Hmy: string;
      BU: string;
      Property: string;
      Region: string;
      Address: string;
      City: string;
      State: string;
      ZipCode: string;
      Market: string;
      RVP: string;
      VP: string;
      RPM: string;
      RegionalMarketingDirector: string;
      Owner: string;
      SecondaryOwner: string;
      Accountant: string;
      BeginningOfOperations: string;
      EndOfOperations: string | null;
      GoLive: string;
      Age: string;
      Class: string;
      AssetType: string;
      UnitCount: string;
      PropertyType: string;
      google_place_id: string;
      google_id: string;
      google_name: string;
      google_address: string;
      census_market: string;
      census_property_id: string;
      census_submarket: string;
      msa_id: string;
      msa_property_id: string;
      msa_property_name: string;
      msa_zip: string;
      start_date: string;
      end_date: string | null;
      is_active: boolean;
    }>;
    dates: Array<{
      year: number;
      month: number;
      day_name: string;
      month_name: string;
      date: string;
    }>;
    systems: Array<{
      System: string;
      property_hmy: string;
      BU: string;
      PropertyName: string;
    }>;
  };
}

export const getScoreCardFilterOptions = async (
  body?: FilterOptionsRequest,
) => {
  try {
    const response = await axios.post<FilterOptionsResponse>(
      `${URL}/score-card-filter-options`,
      body || {},
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching score card filter options:', error);
    return {
      data: {
        properties: [],
        dates: [],
        systems: [],
      },
    };
  }
};

interface SubmarketOccupancyRequest {
  startDate: string; // YYYY-MM-DD format
  endDate: string; // YYYY-MM-DD format
  subMarketName: string;
}

interface SubmarketOccupancyResponse {
  data: Array<{
    submarket_occupancy: number;
  }>;
}

export const getSubmarketOccupancy = async (
  body: SubmarketOccupancyRequest,
) => {
  try {
    const response = await axios.post<SubmarketOccupancyResponse>(
      `${URL}/submarket-occupancy`,
      body,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching submarket occupancy:', error);
    return {
      data: [],
    };
  }
};
